# SAT推荐系统测试环境配置
# 该文件定义了所有环境共享的基础配置结构和默认值
# 环境特定的配置将覆盖这些值

app:
  name: "sat-recommendation-test"
  namespace: "test"
  log_level: "debug"  # 测试环境使用debug日志级别
  max_threads: 10
  shutdown_timeout_secs: 30
  # API服务配置
  host: "0.0.0.0"
  port: 7855       # 测试环境使用7855端口
  cache_ttl_seconds: 3600
  # 以下配置已废弃，保留是为了向后兼容
  # service_host: "0.0.0.0"
  # service_port: 7855

# 测试环境日志配置
logging:
  level: "info"
  file_enabled: true
  file_path: "logs/test-app.log"  # 测试环境使用专用日志文件
  format: "text"  # 测试环境使用文本格式便于直接阅读
  console_enabled: true
  # 测试环境调整日志级别
  component_levels:
    # 将API配置日志调整为debug级别
    api::config: "info"
    # 将核心缓存服务日志调整为debug级别
    core::services::cache: "info"
    # 将API路由日志调整为debug级别
    api::router: "info"
    # 将知识服务日志调整为debug级别
    core::services::knowledge: "info"
    # 将Nacos服务发现日志调整为warn级别
    nacos_sdk: "warn"
    # SQL查询保留debug级别
    sqlx::query: "warn"
    # 将算法日志调整为debug级别
    recommendation_core.algorithms: "debug"

databases:
  # 测试环境数据库配置
  main:
    type: "postgres"
    url: "*************************************************/postgres"
    host: "**************"
    port: "55432"
    user: "postgres"
    password: "postgres"
    name: "postgres"
    prefix: "dev_"
    max_connections: 10
    min_connections: 2
    connection_timeout: 30
    idle_timeout: 600
    statement_timeout: 30
    application_name: "sat_rec_dev"
    is_read_only: false
    enabled: true
  
  # 测试环境不启用只读副本
  readonly:
    enabled: false
  
  # ElasticSearch配置
  elasticsearch:
    type: "elasticsearch"
    hosts: 
      - "http://test.es.knowbuddy.fun:19200"
    index_prefix: "sat_rec_dev_"
    elasticsearch_host: "test.es.knowbuddy.fun"
    elasticsearch_port: 19200
    request_timeout: 30
    retry_on_conflict: 3
    enabled: false


cache:
  # 测试环境Redis配置
  redis:
    type: "redis"
    url: "redis://:E3CTCB2fhci0N7mY@**************:16379"  # 测试环境Redis服务器
    pool_size: 10
    ttl: 600
    cache_ttl: 600
    prefix: "sat_rec_test:"
    database: 3  # 使用测试专用数据库
    enabled: true

storage:
  # 对象存储配置 (S3兼容)
  object_store:
    type: "s3"
    endpoint: "http://localhost:9000"
    bucket: "sat-rec"
    access_key: "minioadmin"
    secret_key: "minioadmin"
    region: "us-east-1"
    s3_endpoint: "http://localhost:9000"
    s3_access_key: "minioadmin"
    s3_secret_key: "minioadmin"
    s3_region: "us-east-1"
    enabled: false

service_discovery:
  enabled: false
  nacos:
    server_addr: "************:8848"
    namespace: "5fd42f7e-1526-438f-b8dd-eeda74d6b5b2"
    group: "DEFAULT_GROUP"
    cluster_name: "DEFAULT"
    service_name: "recommendation-api"
    weight: 1.0
    ephemeral: true
    heartbeat_interval_secs: 5
    metadata:
      version: "1.0.0"
      preserved.register.source: "RUST"
      type: "recommendation"
      description: "推荐系统API服务"

# 告警系统配置
notifications:
  # 总开关，是否启用告警
  enabled: true
  # 是否允许记录错误到日志
  log_errors: true
  # 飞书告警配置
  feishu:
    # 是否启用飞书告警
    enabled: true
    # 飞书机器人Webhook URL
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/7a1a26a4-6030-4d86-8cd2-f5fe2aca4fc7"
    # 飞书机器人密钥（可选，如启用了签名验证则需提供）
    secret: ""
    # 飞书机器人关键词（可选，多个关键词用英文逗号分隔）
    keywords:
      - "错误"
      - "告警"
      - "SAT"
    # 消息标题前缀
    title_prefix: "SAT推荐系统"
    # 最小告警级别: info, warning, error, critical
    min_level: "error"
    # 告警超时（秒）
    timeout_secs: 10
    # 是否重试
    retry_enabled: true
    # 最大重试次数
    max_retries: 3
    # 重试间隔（毫秒）
    retry_interval_ms: 1000

# 测试环境业务逻辑配置
business:
  # 推荐系统业务配置
  recommendation:
    max_recommendations: 10
    default_difficulty_range: 0.5
    popularity_weight: 0.3
    knowledge_coverage_weight: 0.7
    elo_rating_enabled: true
    irt_model_enabled: true
    default_elo: 1700
    k_factor: 32
    problem_k_factor: 32
    scale_factor: 100
    min_k_ratio: 0.5
    use_dynamic_k: true
    knowledge_elo_cache_ttl: 3600
    # 添加ELO算法缺失的参数
    min_rating: 900.0
    max_rating: 2500.0
    max_k_factor: 150.0
    # 添加题目难度相关参数
    initial_question_rating: 1700.0
    min_question_rating: 900.0
    max_question_rating: 2100.0
    # 默认题目状态过滤值
    default_status: 5
    # 学习阶段阈值配置 - 测试环境使用更短的阶段间隔
    stage_thresholds:
      # 冷启动阶段最大题数(0-3题)：加快冷启动阶段
      cold_start_max: 3
      # 过渡阶段I最大题数(4-8题)：缩短过渡阶段
      transition_i_max: 8
      # 过渡阶段II最大题数(9-15题)：缩短过渡阶段
      transition_ii_max: 15
      # 常规学习阶段最大题数(16-40题)：缩短常规阶段
      regular_max: 40
      # 稳定学习阶段最大题数(41-150题)：缩短稳定阶段
      stable_max: 150
    default_algorithm: "random"
    cold_start_threshold: 10
    cold_start_algorithm: "random"
    algorithm_weights:
      ability_match: 0.7
      random: 0.3

  
  # 训练配置
  training:
    irt_batch_size: 100
    min_data_points: 30
    training_schedule: "0 0 * * *"
    auto_training_enabled: true  

# 外部服务配置
external_services:
  # 胜利连胜API服务
  win_streak_api:
    # 服务URL (替代host、port和base_path)
    service_url: "http://ai-service:80/alphaTest"
    # 以下配置已废弃，保留是为了向后兼容
    # host: "**************"
    # port: 31201
    # base_path: "/sat"
    # 请求超时时间（秒）
    timeout_secs: 5
    # 请求重试次数
    retry_count: 3
    # 是否启用
    enabled: true  
