//! 考试应用服务数据传输对象
//!
//! 定义API层和应用层之间的数据传输结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::domain::exam::value_objects::{ExamType, ModuleType, Subject, ModuleStatus, SessionStatus};

/// 创建考试会话请求DTO
#[derive(Debug, Deserialize, Serialize)]
pub struct CreateExamSessionRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
}

/// 创建考试会话响应DTO
#[derive(Debug, Serialize)]
pub struct CreateExamSessionResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 总题数
    pub total_questions: i32,
    /// 总时长（分钟）
    pub total_time_minutes: i32,
    /// 学科信息
    pub sections: Vec<SectionInfoDto>,
}

/// 学科信息DTO
#[derive(Debug, Serialize)]
pub struct SectionInfoDto {
    /// 学科名称
    pub section_name: String,
    /// 学科
    pub subject: Subject,
    /// 模块列表
    pub modules: Vec<ModuleInfoDto>,
}

/// 模块信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleInfoDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目数量
    pub question_count: i32,
    /// 时间限制（分钟）
    pub time_limit_minutes: i32,
    /// 难度级别
    pub difficulty_level: String,
    /// 模块状态
    pub status: ModuleStatus,
    /// 自适应信息（仅对第二模块有效）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub adaptive_info: Option<AdaptiveModuleInfoDto>,
}

/// 自适应模块信息DTO
#[derive(Debug, Serialize)]
pub struct AdaptiveModuleInfoDto {
    /// 依赖的模块类型
    pub depends_on: ModuleType,
    /// 可能的模块类型
    pub possible_types: Vec<ModuleType>,
    /// 实际确定的模块类型（如果已确定）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_type: Option<ModuleType>,
    /// 表现分数（如果已计算）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub performance_score: Option<f32>,
}

/// 恢复考试会话请求DTO
#[derive(Debug, Deserialize)]
pub struct ResumeExamSessionRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 恢复考试会话响应DTO
#[derive(Debug, Serialize)]
pub struct ResumeExamSessionResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 考试状态
    pub exam_state: ExamStateDto,
}

/// 考试会话DTO
#[derive(Debug, Serialize)]
pub struct ExamSessionDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 当前学科
    pub current_subject: Subject,
    /// 当前模块类型
    pub current_module_type: Option<ModuleType>,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 语言部分用时（秒）
    pub reading_time_seconds: i32,
    /// 数学部分用时（秒）
    pub math_time_seconds: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
}

/// 考试状态DTO
#[derive(Debug, Serialize)]
pub struct ExamStateDto {
    /// 当前模块信息
    pub current_module: Option<ModuleProgressDto>,
    /// 所有模块进度
    pub module_progresses: Vec<ModuleProgressDto>,
    /// 已答题目统计
    pub answer_summary: AnswerSummaryDto,
}

/// 模块进度DTO
#[derive(Debug, Serialize)]
pub struct ModuleProgressDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 学科
    pub subject: Subject,
    /// 学科名称
    pub subject_name: String,
    /// 总题数
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 答对题数
    pub correct_questions: i32,
    /// 时间限制（秒）
    pub time_limit_seconds: i32,
    /// 已用时间（秒）
    pub time_used_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_time_seconds: Option<i32>,
    /// 模块状态
    pub module_status: ModuleStatus,
    /// 开始时间
    pub started_at: Option<DateTime<Utc>>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
}

/// 答题统计DTO
#[derive(Debug, Serialize)]
pub struct AnswerSummaryDto {
    /// 总题数
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 答对题数
    pub correct_questions: i32,
    /// 跳过题数
    pub skipped_questions: i32,
    /// 正确率
    pub accuracy_rate: f64,
}

/// 考试题目DTO - 组合ApiQuestionContent和exam特有字段
#[derive(Debug, Serialize)]
pub struct ExamQuestionDto {
    /// 基础题目内容（使用推荐系统的统一格式）
    #[serde(flatten)]
    pub question: crate::infrastructure::dto::question::ApiQuestionContent,
    /// 模块内顺序（考试特有字段）
    pub module_sequence: i32,
}

impl ExamQuestionDto {
    /// 从 QuestionContentDetail 创建考试题目DTO
    pub fn from_question_detail(
        detail: &crate::domain::exam::repository::QuestionContentDetail,
        module_sequence: i32,
        include_answers: bool,
    ) -> Self {
        // 使用core模块的ApiQuestionContent
        let api_question = crate::infrastructure::dto::question::ApiQuestionContent::from_question_content(
            crate::infrastructure::dto::question::QuestionContent {
                id: detail.question_id.to_string(),
                subject_id: detail.subject_id,
                knowledge_id: detail.knowledge_id,
                type_id: detail.type_id,
                difficulty: detail.difficulty as f64,
                question_content: detail.question_content.clone(),
                options: detail.options.clone().unwrap_or(serde_json::Value::Array(vec![])),
                answer: if include_answers { detail.answer.clone() } else { serde_json::Value::Null },
                explanation: if include_answers {
                    detail.explanation.clone().unwrap_or(serde_json::Value::Array(vec![]))
                } else {
                    serde_json::Value::Array(vec![])
                },
                elo_rating: detail.elo_rating,
                usage_count: detail.usage_count,
                correct_count: detail.correct_count,
                question_set: detail.question_set.clone(),
                url: detail.url.clone(),
                is_active: detail.is_active,
                created_at: detail.created_at,
                updated_at: detail.updated_at,
                section_id: detail.section_id,
                metadata: None,
            }
        );

        Self {
            question: api_question,
            module_sequence,
        }
    }
}

/// 开始模块请求DTO
#[derive(Debug, Deserialize)]
pub struct StartModuleRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: ModuleType,
    /// 用户ID
    pub user_id: i64,
}

/// 开始模块响应DTO
#[derive(Debug, Serialize)]
pub struct StartModuleResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 模块信息
    pub module_info: ModuleInfoDto,
    /// 题目列表
    pub questions: Vec<ExamQuestionDto>,
    /// 已答题目索引
    pub answered_questions: Vec<i32>,
    /// 剩余时间（秒）
    pub remaining_time_seconds: i32,
    /// 当前题目索引（从1开始）
    pub current_question_index: i32,
}

/// 通用错误响应DTO
#[derive(Debug, Serialize)]
pub struct ErrorResponseDto {
    /// 错误码
    pub error_code: String,
    /// 错误消息
    pub message: String,
    /// 详细信息
    pub details: Option<serde_json::Value>,
}

/// 提交答案请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitAnswerRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: String,
    /// 答题用时（秒）
    pub time_spent_seconds: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目在模块中的序号
    pub question_sequence: i32,
}

/// 提交答案响应DTO
#[derive(Debug, Serialize)]
pub struct SubmitAnswerResponseDto {
    /// 是否提交成功
    pub success: bool,
    /// 是否正确
    pub is_correct: bool,
    /// 正确答案
    pub correct_answer: String,
    /// 解析说明
    pub explanation: Option<String>,
    /// 当前模块进度
    pub module_progress: ModuleProgressDto,
}

/// 提交考试请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitExamRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 考试成绩DTO
#[derive(Debug, Serialize)]
pub struct ExamScoreDto {
    /// 总分
    pub total_score: i32,
    /// 满分
    pub max_score: i32,
    /// 百分比得分
    pub percentage: f64,
    /// 各学科得分
    pub subject_scores: Vec<SubjectScoreDto>,
}

/// 学科成绩DTO
#[derive(Debug, Serialize)]
pub struct SubjectScoreDto {
    /// 学科
    pub subject: Subject,
    /// 学科名称
    pub subject_name: String,
    /// 得分
    pub score: i32,
    /// 满分
    pub max_score: i32,
    /// 正确题数
    pub correct_count: i32,
    /// 总题数
    pub total_count: i32,
    /// 正确率
    pub accuracy_rate: f64,
}

/// 考试统计DTO
#[derive(Debug, Serialize)]
pub struct ExamStatisticsDto {
    /// 总答题数
    pub total_answered: i32,
    /// 总题数
    pub total_questions: i32,
    /// 总正确数
    pub total_correct: i32,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 平均每题用时（秒）
    pub average_time_per_question: f64,
    /// 完成率
    pub completion_rate: f64,
    /// 正确率
    pub accuracy_rate: f64,
}

/// 提交考试响应DTO
#[derive(Debug, Serialize)]
pub struct SubmitExamResponseDto {
    /// 提交是否成功
    pub success: bool,
    /// 会话ID
    pub session_id: String,
    /// 考试成绩
    pub exam_score: ExamScoreDto,
    /// 考试统计
    pub statistics: ExamStatisticsDto,
    /// 提交时间
    pub submitted_at: DateTime<Utc>,
    /// 考试总用时（秒）
    pub total_duration_seconds: i32,
    /// 考试状态
    pub exam_status: String,
}

/// 用户状态请求DTO
#[derive(Debug, Deserialize)]
pub struct GetUserStatusRequestDto {
    /// 用户ID
    pub user_id: i64,
}

/// 用户状态响应DTO
#[derive(Debug, Serialize)]
pub struct GetUserStatusResponseDto {
    /// 用户ID
    pub user_id: i64,
    /// 历史考试记录
    pub exam_history: Vec<ExamHistoryDto>,
    /// 未完成的会话
    pub incomplete_sessions: Vec<IncompleteSessionDto>,
    /// 统计信息
    pub statistics: UserExamStatisticsDto,
}

/// 考试历史记录DTO
#[derive(Debug, Serialize)]
pub struct ExamHistoryDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 总分
    pub total_score: Option<i32>,
    /// 语言分数
    pub reading_score: Option<i32>,
    /// 数学分数
    pub math_score: Option<i32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 总用时（秒）
    pub total_time_seconds: Option<i32>,
    /// 考试状态
    pub exam_status: String,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
}

/// 未完成会话DTO
#[derive(Debug, Serialize)]
pub struct IncompleteSessionDto {
    /// 会话ID
    pub session_id: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 当前学科
    pub current_subject: Subject,
    /// 当前模块类型
    pub current_module_type: Option<ModuleType>,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 进度信息
    pub progress: SessionProgressDto,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后活动时间
    pub last_activity: DateTime<Utc>,
}

/// 会话进度DTO
#[derive(Debug, Serialize)]
pub struct SessionProgressDto {
    /// 已完成的模块数
    pub completed_modules: i32,
    /// 总模块数
    pub total_modules: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 总题数
    pub total_questions: i32,
    /// 完成百分比
    pub completion_percentage: f64,
}

/// 用户考试统计DTO
#[derive(Debug, Serialize)]
pub struct UserExamStatisticsDto {
    /// 总考试次数
    pub total_exams: i32,
    /// 已完成考试次数
    pub completed_exams: i32,
    /// 未完成考试次数
    pub incomplete_exams: i32,
    /// 平均分数
    pub average_score: Option<f64>,
    /// 最高分数
    pub highest_score: Option<i32>,
    /// 最近考试时间
    pub last_exam_date: Option<DateTime<Utc>>,
    /// 总学习时间（秒）
    pub total_study_time_seconds: i64,
}

/// 题库指引请求DTO
#[derive(Debug, Deserialize)]
pub struct ExamGuideRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 试卷ID（必须提供，不允许使用默认值）
    pub paper_id: i64,
    /// 可选：指定学科（用于学科特定指引）
    pub subject: Option<Subject>,
    /// 可选：现有会话ID（用于学科指引时不创建新会话）
    pub session_id: Option<String>,
}

/// 题库指引响应DTO
#[derive(Debug, Serialize)]
pub struct ExamGuideResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 总题数
    pub total_questions: i32,
    /// 总时间（分钟）
    pub total_time_minutes: i32,
    /// 学科信息
    pub sections_info: Vec<MockExamSectionInfoDto>,
    /// 考试说明
    pub instructions: ExamInstructionsDto,
}

/// 模考学科信息DTO
#[derive(Debug, Serialize)]
pub struct MockExamSectionInfoDto {
    /// 学科名称
    pub section_name: String,
    /// 模块数量
    pub modules: i32,
    /// 每模块题数
    pub questions_per_module: i32,
    /// 每模块时间（分钟）
    pub time_per_module: i32,
    /// 学科提示信息
    pub instructions: String,
}

/// 考试说明DTO
#[derive(Debug, Serialize)]
pub struct ExamInstructionsDto {
    /// 全长模考说明
    pub full_exam_note: Option<String>,
    /// 休息信息
    pub break_info: Option<String>,
    /// 计时器说明
    pub timer_note: String,
}

/// 提交模块请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitModuleRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型
    pub module_type: ModuleType,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交模块响应DTO
#[derive(Debug, Serialize)]
pub struct SubmitModuleResponseDto {
    /// 提交是否成功
    pub success: bool,
    /// 模块成绩信息
    pub module_score: ModuleScoreDto,
    /// 模块统计信息
    pub module_statistics: ModuleStatisticsDto,
    /// 下一步操作信息
    pub next_action: NextActionDto,
    /// 自适应信息（如果是第一模块）
    pub adaptive_info: Option<AdaptiveResultDto>,
    /// 下一个模块信息（如果有下一个模块）
    pub next_module_info: Option<NextModuleInfoDto>,
}

/// 模块成绩DTO
#[derive(Debug, Serialize)]
pub struct ModuleScoreDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 学科
    pub subject: Subject,
    /// 学科名称
    pub subject_name: String,
    /// 原始分数（答对题数）
    pub raw_score: i32,
    /// 满分
    pub max_score: i32,
    /// 正确率
    pub accuracy_rate: f64,
    /// SAT分数（如果适用）
    pub sat_score: Option<i32>,
    /// 分数区间（如果适用）
    pub score_range: Option<ScoreRangeDto>,
}

/// 分数区间DTO
#[derive(Debug, Serialize)]
pub struct ScoreRangeDto {
    /// 最低分
    pub min_score: i32,
    /// 最高分
    pub max_score: i32,
}

/// 模块统计DTO
#[derive(Debug, Serialize)]
pub struct ModuleStatisticsDto {
    /// 总题数
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 答对题数
    pub correct_questions: i32,
    /// 跳过题数
    pub skipped_questions: i32,
    /// 平均答题时间（秒）
    pub average_time_per_question: f64,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_time_seconds: i32,
    /// 完成率
    pub completion_rate: f64,
    /// 正确率
    pub accuracy_rate: f64,
}

/// 下一步操作DTO
#[derive(Debug, Clone, Serialize)]
pub struct NextActionDto {
    /// 操作类型
    pub action_type: NextActionType,
    /// 操作描述
    pub description: String,
    /// 是否可以继续
    pub can_continue: bool,
    /// 建议等待时间（秒，用于休息时间）
    pub suggested_wait_seconds: Option<i32>,
}

/// 下一步操作类型
#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum NextActionType {
    /// 开始第二模块
    StartModule2,
    /// 切换学科
    SwitchSubject,
    /// 休息时间
    TakeBreak,
    /// 完成考试
    CompleteExam,
    /// 等待评分
    WaitForScoring,
}

/// 自适应结果DTO
#[derive(Debug, Serialize)]
pub struct AdaptiveResultDto {
    /// 第一模块表现分数
    pub module1_performance_score: f64,
    /// 确定的第二模块类型
    pub module2_type: ModuleType,
    /// 自适应原因
    pub adaptive_reason: String,
    /// 阈值信息
    pub threshold_info: ThresholdInfoDto,
}

/// 阈值信息DTO
#[derive(Debug, Serialize)]
pub struct ThresholdInfoDto {
    /// 使用的阈值
    pub threshold: f64,
    /// 实际得分
    pub actual_score: f64,
    /// 是否达到阈值
    pub threshold_met: bool,
}

/// 下一个模块信息DTO
#[derive(Debug, Serialize)]
pub struct NextModuleInfoDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 学科
    pub subject: Subject,
    /// 学科名称
    pub subject_name: String,
    /// 题目数量
    pub question_count: i32,
    /// 时间限制（分钟）
    pub time_limit_minutes: i32,
    /// 难度级别描述
    pub difficulty_level: String,
    /// 模块描述
    pub description: String,
    /// 是否可以立即开始
    pub can_start_immediately: bool,
    /// 建议等待时间（秒，如果不能立即开始）
    pub suggested_wait_seconds: Option<i32>,
}

/// 模块成绩查询响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleScoreResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: ModuleType,
    /// 模块状态
    pub module_status: String,
    /// 成绩信息（如果已提交）
    pub score_info: Option<ModuleScoreInfoDto>,
}

/// 模块成绩信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleScoreInfoDto {
    /// 原始分数
    pub raw_score: i32,
    /// 满分
    pub max_score: i32,
    /// 正确率
    pub accuracy_rate: f64,
    /// SAT分数
    pub sat_score: Option<i32>,
    /// 分数区间
    pub score_range: Option<ScoreRangeDto>,
}

/// 模块统计查询响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleStatisticsResponseDto {
    /// 会话ID
    pub session_id: String,
    /// 模块类型
    pub module_type: ModuleType,
    /// 统计信息
    pub statistics: ModuleStatisticsInfoDto,
}

/// 模块统计信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleStatisticsInfoDto {
    /// 总题数
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 答对题数
    pub correct_questions: i32,
    /// 跳过题数
    pub skipped_questions: i32,
    /// 平均答题时间（秒）
    pub average_time_per_question: f64,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_time_seconds: i32,
    /// 完成率
    pub completion_rate: f64,
    /// 正确率
    pub accuracy_rate: f64,
}

/// 获取试卷题目请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPaperQuestionsRequestDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 是否包含答案（默认false，考试时不返回答案）
    pub include_answers: Option<bool>,
    /// 模块类型过滤（可选）
    pub module_type: Option<String>,
    /// 学科过滤（可选）
    pub subject_id: Option<i32>,
}

/// 获取试卷题目响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPaperQuestionsResponseDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 试卷版本
    pub version: i32,
    /// 题目列表
    pub questions: Vec<ExamQuestionContentDto>,
    /// 总题目数
    pub total_count: i32,
    /// 按学科分组的统计
    pub subject_stats: Vec<SubjectStatDto>,
}

/// 考试题目内容DTO（扩展版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamQuestionContentDto {
    /// 题目ID
    pub id: i32,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 题目内容
    pub content: serde_json::Value,
    /// 选项
    pub options: Option<serde_json::Value>,
    /// 答案（考试时不返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub answer: Option<serde_json::Value>,
    /// 解析（考试时不返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub explanation: Option<serde_json::Value>,
    /// ELO评分
    pub elo_rating: f64,
    /// 模块内顺序
    pub module_sequence: i32,
    /// 模块类型
    pub module_type: String,
    /// 难度等级
    pub difficulty: i16,
    /// 是否激活
    pub is_active: bool,
}

/// 学科统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubjectStatDto {
    /// 学科ID
    pub subject_id: i32,
    /// 学科名称
    pub subject_name: String,
    /// 题目数量
    pub question_count: i32,
    /// 模块统计
    pub module_stats: Vec<ModuleStatDto>,
}

/// 模块统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleStatDto {
    /// 模块类型
    pub module_type: String,
    /// 题目数量
    pub question_count: i32,
}

// ==================== 优化接口DTO ====================

/// 考试准备请求DTO
#[derive(Debug, Deserialize)]
pub struct ExamPrepareRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 是否自动恢复未完成会话
    pub auto_resume: Option<bool>,
    /// 恢复的会话ID（可选，如果指定则尝试恢复该会话）
    pub resume_session_id: Option<String>,
    /// 是否直接开始第一模块
    pub auto_start_first_module: Option<bool>,
    /// 是否包含用户历史记录
    pub include_user_history: Option<bool>,
}



/// 准备操作类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum PrepareActionType {
    /// 创建新会话
    CreateNew,
    /// 恢复现有会话
    ResumeExisting,
    /// 取消旧会话并创建新会话
    CancelAndCreateNew,
}

/// 会话信息DTO
#[derive(Debug, Serialize)]
pub struct SessionInfoDto {
    /// 会话ID
    pub session_id: String,
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: ExamType,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 总题数
    pub total_questions: i32,
    /// 总时长（分钟）
    pub total_time_minutes: i32,
}



/// 模块切换请求DTO
#[derive(Debug, Deserialize)]
pub struct ModuleTransitionRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 操作类型
    pub action: ModuleTransitionAction,
    /// 当前模块类型（如果需要提交）
    pub current_module_type: Option<ModuleType>,
    /// 下一模块类型（如果需要开始）
    pub next_module_type: Option<ModuleType>,
    /// 是否强制提交当前模块
    pub force_submit: Option<bool>,
}

/// 模块切换操作类型
#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum ModuleTransitionAction {
    /// 仅提交当前模块
    SubmitOnly,
    /// 仅开始下一模块
    StartNext,
    /// 提交当前模块并开始下一模块
    SubmitAndNext,
}

/// 模块切换响应DTO
#[derive(Debug, Serialize)]
pub struct ModuleTransitionResponseDto {
    /// 操作成功
    pub success: bool,
    /// 执行的操作
    pub executed_actions: Vec<String>,
    /// 提交的模块结果（如果有提交操作）
    pub submit_result: Option<SubmitModuleResponseDto>,
    /// 开始的模块信息（如果有开始操作）
    pub start_result: Option<StartModuleResponseDto>,
    /// 完整考试状态
    pub exam_state: ExamStateDto,
    /// 下一步建议操作
    pub next_action: NextActionDto,
    /// 操作消息
    pub message: String,
}

/// 增强答题提交请求DTO
#[derive(Debug, Deserialize)]
pub struct EnhancedSubmitAnswerRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: String,
    /// 答题用时（秒）
    pub time_spent_seconds: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目序号
    pub question_sequence: i32,
    /// 是否返回完整会话状态
    pub include_session_state: Option<bool>,
    /// 是否返回下一题信息
    pub include_next_question: Option<bool>,
}

/// 增强答题提交响应DTO
#[derive(Debug, Serialize)]
pub struct EnhancedSubmitAnswerResponseDto {
    /// 基础答题结果
    pub answer_result: SubmitAnswerResponseDto,
    /// 完整会话状态（可选）
    pub session_state: Option<ExamStateDto>,
    /// 下一题信息（可选）
    pub next_question: Option<ExamQuestionDto>,
    /// 模块完成状态
    pub module_completion: ModuleCompletionStatusDto,
    /// 下一步建议操作
    pub suggested_action: SuggestedActionDto,
}

/// 模块完成状态DTO
#[derive(Debug, Serialize)]
pub struct ModuleCompletionStatusDto {
    /// 是否完成当前模块
    pub is_module_completed: bool,
    /// 模块完成百分比
    pub completion_percentage: f64,
    /// 剩余题目数
    pub remaining_questions: i32,
    /// 是否可以提交模块
    pub can_submit_module: bool,
}

/// 建议操作DTO
#[derive(Debug, Serialize)]
pub struct SuggestedActionDto {
    /// 操作类型
    pub action_type: SuggestedActionType,
    /// 操作描述
    pub description: String,
    /// 是否紧急
    pub is_urgent: bool,
    /// 相关参数
    pub parameters: Option<serde_json::Value>,
}

/// 建议操作类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum SuggestedActionType {
    /// 继续答题
    ContinueAnswering,
    /// 提交模块
    SubmitModule,
    /// 切换模块
    SwitchModule,
    /// 休息一下
    TakeBreak,
    /// 完成考试
    CompleteExam,
}



/// 模块详细信息DTO
#[derive(Debug, Serialize)]
pub struct ModuleDetailDto {
    /// 模块类型
    pub module_type: ModuleType,
    /// 模块状态
    pub module_status: ModuleStatus,
    /// 学科信息
    pub subject: Subject,
    /// 学科名称
    pub subject_name: String,
    /// 进度信息
    pub progress: ModuleProgressDto,
    /// 成绩信息（如果已提交）
    pub score: Option<ModuleScoreDto>,
    /// 统计信息
    pub statistics: ModuleStatisticsDto,
    /// 是否当前活跃模块
    pub is_current: bool,
    /// 是否可以开始
    pub can_start: bool,
    /// 是否可以提交
    pub can_submit: bool,
}

/// 答题历史DTO
#[derive(Debug, Serialize)]
pub struct AnswerHistoryDto {
    /// 题目ID
    pub question_id: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目序号
    pub question_sequence: i32,
    /// 学生答案
    pub student_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题用时（秒）
    pub time_spent_seconds: Option<i32>,
    /// 答题时间
    pub answered_at: Option<DateTime<Utc>>,
    /// 题目内容摘要
    pub question_summary: Option<String>,
}

/// 时间信息DTO
#[derive(Debug, Serialize)]
pub struct TimeInfoDto {
    /// 考试开始时间
    pub exam_start_time: DateTime<Utc>,
    /// 当前时间
    pub current_time: DateTime<Utc>,
    /// 已用时间（秒）
    pub elapsed_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_seconds: i32,
    /// 是否接近超时
    pub is_near_timeout: bool,
    /// 超时警告阈值（秒）
    pub timeout_warning_threshold: i32,
}

/// 推荐操作DTO
#[derive(Debug, Serialize)]
pub struct RecommendationDto {
    /// 推荐类型
    pub recommendation_type: RecommendationType,
    /// 推荐标题
    pub title: String,
    /// 推荐描述
    pub description: String,
    /// 优先级（1-5，5最高）
    pub priority: i32,
    /// 相关参数
    pub parameters: Option<serde_json::Value>,
}

/// 推荐类型
#[derive(Debug, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum RecommendationType {
    /// 时间管理
    TimeManagement,
    /// 答题策略
    AnsweringStrategy,
    /// 模块切换
    ModuleTransition,
    /// 休息建议
    BreakSuggestion,
    /// 完成提醒
    CompletionReminder,
}

// ============================================================================
// 考试记录相关DTO
// ============================================================================

/// 获取考试记录请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID（可选，用于筛选特定试卷）
    pub paper_id: Option<i64>,
    /// 考试类型（可选，用于筛选特定类型）
    pub exam_type: Option<String>,
    /// 页码（从1开始）
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 获取指定试卷考试记录请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPaperExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID列表
    pub paper_ids: Vec<i64>,
}

/// 考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamRecordsResponseDto {
    /// 考试记录统计列表
    pub paper_statistics: Vec<PaperStatisticsDto>,
    /// 分页信息
    pub pagination: PaginationDto,
}

/// 指定试卷考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperExamRecordsResponseDto {
    /// 考试记录统计列表（不分页）
    pub paper_statistics: Vec<PaperStatisticsDto>,
}

/// 试卷统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperStatisticsDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试次数
    pub exam_count: u32,
    /// 完成次数
    pub completed_count: u32,
    /// 最高分
    pub best_score: Option<u32>,
    /// 最新分数
    pub latest_score: Option<u32>,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最近考试详情
    pub last_exam_detail: Option<LastExamDetailDto>,
    /// 进步趋势
    pub progress_trend: ProgressTrendDto,
}

/// 最近考试详情DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LastExamDetailDto {
    /// 会话ID
    pub session_id: String,
    /// 总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 考试进度
    pub exam_progress: Option<f64>,
    /// 考试状态
    pub exam_status: String,
}

/// 进步趋势DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressTrendDto {
    /// 是否在进步
    pub is_improving: bool,
    /// 分数变化
    pub score_change: i32,
    /// 趋势描述
    pub trend_description: String,
}

/// 分页信息DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationDto {
    /// 当前页码
    pub current_page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总记录数
    pub total_count: u64,
    /// 总页数
    pub total_pages: u32,
}
