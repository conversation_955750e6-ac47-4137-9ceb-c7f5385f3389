//! 考试记录相关数据传输对象
//!
//! 定义考试记录查询、统计等相关的DTO结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use super::common::PaginationDto;
use super::score::ExamScoreDto;
use super::statistics::ExamStatisticsDto;

/// 获取考试记录请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID（可选，用于筛选特定试卷）
    pub paper_id: Option<i64>,
    /// 考试类型（可选，用于筛选特定类型）
    pub exam_type: Option<String>,
    /// 页码（从1开始）
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 获取指定试卷考试记录请求DTO
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GetPaperExamRecordsRequestDto {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID列表
    pub paper_ids: Vec<i64>,
}

/// 考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamRecordsResponseDto {
    /// 考试记录统计列表
    pub paper_statistics: Vec<PaperStatisticsDto>,
    /// 分页信息
    pub pagination: PaginationDto,
}

/// 指定试卷考试记录响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperExamRecordsResponseDto {
    /// 考试记录统计列表（不分页）
    pub paper_statistics: Vec<PaperStatisticsDto>,
}

/// 试卷统计DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperStatisticsDto {
    /// 试卷ID
    pub paper_id: i64,
    /// 试卷名称
    pub paper_name: String,
    /// 考试类型
    pub exam_type: String,
    /// 考试次数
    pub exam_count: u32,
    /// 完成次数
    pub completed_count: u32,
    /// 最高分
    pub best_score: Option<u32>,
    /// 最新分数
    pub latest_score: Option<u32>,
    /// 平均分
    pub average_score: Option<f64>,
    /// 最近考试详情
    pub last_exam_detail: Option<LastExamDetailDto>,
    /// 进步趋势
    pub progress_trend: ProgressTrendDto,
}

/// 最近考试详情DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LastExamDetailDto {
    /// 会话ID
    pub session_id: String,
    /// 总分
    pub total_score: Option<u32>,
    /// 阅读分数
    pub reading_score: Option<u32>,
    /// 数学分数
    pub math_score: Option<u32>,
    /// 正确率
    pub accuracy_rate: Option<f64>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 考试时长（分钟）
    pub duration_minutes: Option<u32>,
    /// 考试进度
    pub exam_progress: Option<f64>,
    /// 考试状态
    pub exam_status: String,
}

/// 进步趋势DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressTrendDto {
    /// 是否在进步
    pub is_improving: bool,
    /// 分数变化
    pub score_change: i32,
    /// 趋势描述
    pub trend_description: String,
}

/// 提交考试请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitExamRequestDto {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交考试响应DTO
#[derive(Debug, Serialize)]
pub struct SubmitExamResponseDto {
    /// 提交是否成功
    pub success: bool,
    /// 会话ID
    pub session_id: String,
    /// 考试成绩
    pub exam_score: ExamScoreDto,
    /// 考试统计
    pub statistics: ExamStatisticsDto,
    /// 提交时间
    pub submitted_at: DateTime<Utc>,
    /// 考试总用时（秒）
    pub total_duration_seconds: i32,
    /// 考试状态
    pub exam_status: String,
}
