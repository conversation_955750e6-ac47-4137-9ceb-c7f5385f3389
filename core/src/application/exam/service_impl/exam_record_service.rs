//! 考试记录应用服务实现
//!
//! 提供考试记录查询、统计和分析功能

use std::sync::Arc;
use async_trait::async_trait;
use tracing::{info, warn};
use chrono::Utc;

use crate::error::Result;
use crate::infrastructure::database::models::exam_statistics;
use crate::infrastructure::persistence::database::manager::StorageManager;
use sea_orm::{EntityTrait, QueryFilter, QueryOrder, ColumnTrait, PaginatorTrait, QuerySelect, DatabaseConnection, ConnectionTrait};

use super::super::dto::{
    GetExamRecordsRequestDto, ExamRecordsResponseDto, PaperStatisticsDto,
    LastExamDetailDto, ProgressTrendDto,
    GetPaperExamRecordsRequestDto, PaperExamRecordsResponseDto,
};
use crate::models::Pagination;

/// 考试记录应用服务接口
#[async_trait]
pub trait ExamRecordApplicationService: Send + Sync {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto>;

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto>;

    /// 更新考试统计信息（当考试完成时调用）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()>;

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()>;
}

/// 考试记录应用服务实现
pub struct ExamRecordServiceImpl {
    storage: Arc<StorageManager>,
}

impl ExamRecordServiceImpl {
    /// 创建新的考试记录服务实例
    pub fn new(storage: Arc<StorageManager>) -> Self {
        Self { storage }
    }
}

#[async_trait]
impl ExamRecordApplicationService for ExamRecordServiceImpl {
    /// 获取用户的考试记录列表
    async fn get_exam_records(&self, request: GetExamRecordsRequestDto) -> Result<ExamRecordsResponseDto> {
        info!("获取用户考试记录: user_id={}", request.user_id);
        
        let db = self.storage.sea_orm_db();
        
        // 设置分页参数
        let page = request.page.unwrap_or(1);
        let page_size = request.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;
        
        // 构建查询条件
        let mut query = exam_statistics::exam_record_view::Entity::find()
            .filter(exam_statistics::exam_record_view::Column::UserId.eq(request.user_id));
        
        // 添加试卷ID过滤
        if let Some(paper_id) = request.paper_id {
            query = query.filter(exam_statistics::exam_record_view::Column::PaperId.eq(paper_id));
        }
        
        // 添加考试类型过滤
        if let Some(exam_type) = &request.exam_type {
            query = query.filter(exam_statistics::exam_record_view::Column::ExamType.eq(exam_type));
        }
        
        // 按最近考试时间排序
        query = query.order_by_desc(exam_statistics::exam_record_view::Column::LatestCompletedAt);
        
        // 获取总数
        let total_count = query.clone().count(db.as_ref()).await
            .map_err(|e| crate::error::Error::database(format!("查询考试记录总数失败: {}", e)))?;

        // 分页查询
        let records = query
            .offset(offset as u64)
            .limit(page_size as u64)
            .all(db.as_ref())
            .await
            .map_err(|e| crate::error::Error::database(format!("查询考试记录失败: {}", e)))?;
        
        // 转换为DTO
        let paper_statistics = records.into_iter().map(|record| {
            PaperStatisticsDto {
                paper_id: record.paper_id,
                paper_name: record.paper_name,
                exam_type: record.exam_type,
                exam_count: record.exam_count as u32,
                completed_count: record.completed_count as u32,
                best_score: record.best_score.map(|s| s as u32),
                latest_score: record.latest_score.map(|s| s as u32),
                average_score: record.average_score.map(|s| s.to_string().parse().unwrap_or(0.0)),
                last_exam_detail: record.latest_session_id.map(|session_id| {
                    LastExamDetailDto {
                        session_id,
                        total_score: record.latest_total_score.map(|s| s as u32),
                        reading_score: record.latest_reading_score.map(|s| s as u32),
                        math_score: record.latest_math_score.map(|s| s as u32),
                        accuracy_rate: record.latest_accuracy_rate.map(|r| r.to_string().parse().unwrap_or(0.0)),
                        completed_at: record.latest_completed_at.map(|dt| dt.with_timezone(&Utc)),
                        duration_minutes: record.latest_duration_minutes.map(|d| d as u32),
                        exam_progress: record.latest_exam_progress.map(|p| p.to_string().parse().unwrap_or(0.0)),
                        exam_status: record.latest_exam_status.unwrap_or_else(|| "completed".to_string()),
                    }
                }),
                progress_trend: ProgressTrendDto {
                    is_improving: record.is_improving.unwrap_or(false),
                    score_change: record.score_change,
                    trend_description: record.trend_description.unwrap_or_else(|| "暂无数据".to_string()),
                },
            }
        }).collect();
        
        // 计算总页数
        let total_pages = if total_count == 0 {
            0
        } else {
            ((total_count - 1) / page_size as u64 + 1) as u32
        };
        
        let response = ExamRecordsResponseDto {
            paper_statistics,
            pagination: Pagination {
                page: page as u64,
                per_page: page_size as u64,
                total: total_count,
                total_pages: total_pages as u64,
            },
        };
        
        info!("考试记录查询完成: user_id={}, 记录数={}", request.user_id, response.paper_statistics.len());
        Ok(response)
    }

    /// 获取指定试卷的考试记录（不分页）
    async fn get_paper_exam_records(&self, request: GetPaperExamRecordsRequestDto) -> Result<PaperExamRecordsResponseDto> {
        info!("获取指定用户试卷考试记录: user_id={}, paper_ids={:?}", request.user_id, request.paper_ids);

        let db = self.storage.sea_orm_db();

        // 构建IN子句的占位符
        let placeholders: Vec<String> = (2..=request.paper_ids.len() + 1)
            .map(|i| format!("${}", i))
            .collect();
        let in_clause = placeholders.join(", ");

        // 查询指定用户和试卷列表的所有考试记录统计
        let sql = format!(r#"
            SELECT
                user_id, paper_id, paper_name, exam_type, exam_count, completed_count,
                best_score, latest_score, average_score, score_change, trend_description,
                latest_session_id, latest_total_score, latest_reading_score, latest_math_score,
                latest_accuracy_rate, latest_completed_at, latest_duration_minutes,
                latest_exam_progress, latest_exam_status,
                is_improving
            FROM v_sat_exam_records
            WHERE user_id = $1 AND paper_id IN ({})
            ORDER BY paper_id, exam_type
        "#, in_clause);

        // 构建参数列表
        let mut params = vec![sea_orm::Value::BigInt(Some(request.user_id))];
        for paper_id in request.paper_ids.iter() {
            params.push(sea_orm::Value::BigInt(Some(*paper_id)));
        }

        let stmt = sea_orm::Statement::from_sql_and_values(
            sea_orm::DatabaseBackend::Postgres,
            &sql,
            params
        );

        let results = db.query_all(stmt).await
            .map_err(|e| crate::error::Error::database(format!("查询试卷考试记录失败: {}", e)))?;

        // 转换为DTO
        let paper_statistics: Vec<PaperStatisticsDto> = results.into_iter().map(|record| {
            let session_id = record.try_get::<String>("", "latest_session_id").ok();

            PaperStatisticsDto {
                paper_id: record.try_get("", "paper_id").unwrap_or(0),
                paper_name: record.try_get("", "paper_name").unwrap_or_else(|_| "未知试卷".to_string()),
                exam_type: record.try_get("", "exam_type").unwrap_or_else(|_| "full".to_string()),
                exam_count: record.try_get::<i32>("", "exam_count").unwrap_or(0) as u32,
                completed_count: record.try_get::<i32>("", "completed_count").unwrap_or(0) as u32,
                best_score: record.try_get::<i32>("", "best_score").ok().map(|s| s as u32),
                latest_score: record.try_get::<i32>("", "latest_score").ok().map(|s| s as u32),
                average_score: record.try_get::<f64>("", "average_score").ok(),
                last_exam_detail: session_id.map(|session_id| {
                    LastExamDetailDto {
                        session_id,
                        total_score: record.try_get::<i32>("", "latest_total_score").ok().map(|s| s as u32),
                        reading_score: record.try_get::<i32>("", "latest_reading_score").ok().map(|s| s as u32),
                        math_score: record.try_get::<i32>("", "latest_math_score").ok().map(|s| s as u32),
                        accuracy_rate: record.try_get::<f64>("", "latest_accuracy_rate").ok(),
                        completed_at: record.try_get::<chrono::DateTime<chrono::Utc>>("", "latest_completed_at").ok().map(|dt| dt.with_timezone(&Utc)),
                        duration_minutes: record.try_get::<i32>("", "latest_duration_minutes").ok().map(|d| d as u32),
                        exam_progress: record.try_get::<f64>("", "latest_exam_progress").ok(),
                        exam_status: record.try_get("", "latest_exam_status").unwrap_or_else(|_| "completed".to_string()),
                    }
                }),
                progress_trend: ProgressTrendDto {
                    is_improving: record.try_get("", "is_improving").unwrap_or(false),
                    score_change: record.try_get::<i32>("", "score_change").unwrap_or(0),
                    trend_description: record.try_get("", "trend_description").unwrap_or_else(|_| "暂无数据".to_string()),
                },
            }
        }).collect();

        let response = PaperExamRecordsResponseDto {
            paper_statistics,
        };

        info!("指定用户试卷考试记录查询完成: user_id={}, paper_ids={:?}, 记录数={}",
            request.user_id, request.paper_ids, response.paper_statistics.len());
        Ok(response)
    }

    /// 更新考试统计信息（应用层计算）
    async fn update_exam_statistics(&self, user_id: i64, paper_id: i64, exam_type: String) -> Result<()> {
        info!("更新考试统计信息: user_id={}, paper_id={}, exam_type={}", user_id, paper_id, exam_type);

        let db = self.storage.sea_orm_db();

        // 1. 从考试历史表查询原始数据进行统计计算
        let sql = r#"
            SELECT
                session_id, paper_name, total_score, total_time_seconds,
                exam_status, completed_at
            FROM t_sat_mock_exam_history
            WHERE user_id = $1 AND paper_id = $2 AND exam_type = $3
            ORDER BY completed_at DESC NULLS LAST
        "#;

        let stmt = sea_orm::Statement::from_sql_and_values(
            sea_orm::DatabaseBackend::Postgres,
            sql,
            vec![
                sea_orm::Value::BigInt(Some(user_id)),
                sea_orm::Value::BigInt(Some(paper_id)),
                sea_orm::Value::String(Some(Box::new(exam_type.clone()))),
            ]
        );

        let results = db.query_all(stmt).await
            .map_err(|e| crate::error::Error::database(format!("查询考试记录失败: {}", e)))?;

        if results.is_empty() {
            info!("未找到考试记录，跳过统计更新: user_id={}, paper_id={}, exam_type={}", user_id, paper_id, exam_type);
            return Ok(());
        }

        // 3. 应用层计算统计数据
        let exam_count = results.len() as i32;
        let completed_count = results.iter()
            .filter(|row| {
                row.try_get::<String>("", "exam_status")
                    .unwrap_or_else(|_| "".to_string()) == "completed"
            })
            .count() as i32;

        let scores: Vec<i32> = results.iter()
            .filter_map(|row| row.try_get::<i32>("", "total_score").ok())
            .collect();

        let best_score = scores.iter().max().copied();
        let latest_score = scores.first().copied();
        let average_score = if !scores.is_empty() {
            Some(scores.iter().sum::<i32>() as f64 / scores.len() as f64)
        } else {
            None
        };

        // 获取最新考试信息
        let latest_record = &results[0];
        let latest_session_id: Option<String> = latest_record.try_get("", "session_id").ok();
        let paper_name: Option<String> = latest_record.try_get("", "paper_name").ok();
        let latest_exam_at = latest_record.try_get::<chrono::DateTime<chrono::Utc>>("", "completed_at").ok();

        // 计算总学习时间（从秒转换为分钟）
        let total_study_time_minutes: i32 = results.iter()
            .filter_map(|row| row.try_get::<i32>("", "total_time_seconds").ok())
            .map(|seconds| (seconds + 30) / 60) // 四舍五入转换为分钟
            .sum();

        let average_duration_minutes = if !results.is_empty() {
            Some(total_study_time_minutes / results.len() as i32)
        } else {
            None
        };

        // 4. 更新或插入统计记录
        self.upsert_exam_statistics(
            user_id,
            paper_id,
            paper_name.unwrap_or_else(|| "未知试卷".to_string()),
            exam_type.clone(),
            exam_count,
            completed_count,
            best_score,
            latest_score,
            average_score,
            latest_session_id,
            latest_exam_at,
            total_study_time_minutes,
            average_duration_minutes,
        ).await?;

        info!("考试统计信息更新完成: user_id={}, paper_id={}, exam_type={}", user_id, paper_id, exam_type);
        Ok(())
    }

    /// 重新计算用户的所有考试统计信息
    async fn recalculate_user_statistics(&self, user_id: i64) -> Result<()> {
        info!("重新计算用户统计信息: user_id={}", user_id);
        
        let db = self.storage.sea_orm_db();

        // 获取用户的所有考试记录组合
        let sql = r#"
            SELECT DISTINCT paper_id, exam_type
            FROM t_sat_mock_exam_history
            WHERE user_id = $1
        "#;

        let stmt = sea_orm::Statement::from_sql_and_values(
            sea_orm::DatabaseBackend::Postgres,
            sql,
            vec![sea_orm::Value::BigInt(Some(user_id))]
        );

        let results = db.query_all(stmt).await
            .map_err(|e| crate::error::Error::database(format!("查询用户考试记录失败: {}", e)))?;

        // 为每个组合更新统计信息
        for row in results {
            let paper_id: i64 = row.try_get("", "paper_id")
                .map_err(|e| crate::error::Error::database(format!("获取paper_id失败: {}", e)))?;
            let exam_type: String = row.try_get("", "exam_type")
                .map_err(|e| crate::error::Error::database(format!("获取exam_type失败: {}", e)))?;

            self.update_exam_statistics(user_id, paper_id, exam_type).await?;
        }
        
        info!("用户统计信息重新计算完成: user_id={}", user_id);
        Ok(())
    }
}

/// 考试记录统计辅助函数
impl ExamRecordServiceImpl {
    /// 更新或插入考试统计记录
    async fn upsert_exam_statistics(
        &self,
        user_id: i64,
        paper_id: i64,
        paper_name: String,
        exam_type: String,
        exam_count: i32,
        completed_count: i32,
        best_score: Option<i32>,
        latest_score: Option<i32>,
        average_score: Option<f64>,
        latest_session_id: Option<String>,
        latest_exam_at: Option<chrono::DateTime<chrono::Utc>>,
        total_study_time_minutes: i32,
        average_duration_minutes: Option<i32>,
    ) -> Result<()> {
        let db = self.storage.sea_orm_db();

        let sql = r#"
            INSERT INTO t_sat_exam_statistics (
                user_id, paper_id, paper_name, exam_type,
                exam_count, completed_count, best_score, latest_score, average_score,
                latest_session_id, latest_exam_at,
                total_study_time_minutes, average_duration_minutes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            ON CONFLICT (user_id, paper_id, exam_type)
            DO UPDATE SET
                exam_count = $5,
                completed_count = $6,
                best_score = $7,
                latest_score = $8,
                average_score = $9,
                latest_session_id = $10,
                latest_exam_at = $11,
                total_study_time_minutes = $12,
                average_duration_minutes = $13,
                updated_at = CURRENT_TIMESTAMP
        "#;

        let stmt = sea_orm::Statement::from_sql_and_values(
            sea_orm::DatabaseBackend::Postgres,
            sql,
            vec![
                sea_orm::Value::BigInt(Some(user_id)),
                sea_orm::Value::BigInt(Some(paper_id)),
                sea_orm::Value::String(Some(Box::new(paper_name))),
                sea_orm::Value::String(Some(Box::new(exam_type))),
                sea_orm::Value::Int(Some(exam_count)),
                sea_orm::Value::Int(Some(completed_count)),
                sea_orm::Value::Int(best_score),
                sea_orm::Value::Int(latest_score),
                sea_orm::Value::Double(average_score),
                sea_orm::Value::String(latest_session_id.map(Box::new)),
                sea_orm::Value::ChronoDateTimeUtc(latest_exam_at.map(Box::new)),
                sea_orm::Value::Int(Some(total_study_time_minutes)),
                sea_orm::Value::Int(average_duration_minutes),
            ]
        );

        db.execute(stmt).await
            .map_err(|e| crate::error::Error::database(format!("更新统计记录失败: {}", e)))?;

        Ok(())
    }
    /// 当考试完成时自动更新统计信息
    pub async fn on_exam_completed(&self, session_id: &str) -> Result<()> {
        info!("考试完成，更新统计信息: session_id={}", session_id);
        
        let db = self.storage.sea_orm_db();
        
        // 从考试历史表获取考试信息
        let sql = r#"
            SELECT user_id, paper_id, exam_type 
            FROM t_sat_mock_exam_history 
            WHERE session_id = $1
        "#;
        
        let stmt = sea_orm::Statement::from_sql_and_values(
            sea_orm::DatabaseBackend::Postgres,
            sql,
            vec![sea_orm::Value::String(Some(Box::new(session_id.to_string())))]
        );
        
        let result = db.query_one(stmt).await
            .map_err(|e| crate::error::Error::database(format!("查询考试信息失败: {}", e)))?;
        
        if let Some(row) = result {
            let user_id: i64 = row.try_get("", "user_id")
                .map_err(|e| crate::error::Error::database(format!("获取user_id失败: {}", e)))?;
            let paper_id: i64 = row.try_get("", "paper_id")
                .map_err(|e| crate::error::Error::database(format!("获取paper_id失败: {}", e)))?;
            let exam_type: String = row.try_get("", "exam_type")
                .map_err(|e| crate::error::Error::database(format!("获取exam_type失败: {}", e)))?;
            
            self.update_exam_statistics(user_id, paper_id, exam_type).await?;
        } else {
            warn!("未找到考试记录: session_id={}", session_id);
        }
        
        Ok(())
    }
}
